class_name Gate2D extends StaticBody2D

#region Render Gate
@export_group("General Gate Information")
@export var gate_name: String = "Gate"
@export var input_pins: Dictionary[String, bool]
@export var output_pins: Dictionary[String, bool]
@export var boolean_expressions: Dictionary

@export_group("Rendering")
@export var distance_btw_pins: int = 32

@onready var input_pin_container = $"input pins"
@onready var output_pin_container = $"output pins"
@onready var label = $Control/Label
@onready var color_rect = $"Control/PanelContainer"
@onready var canvas_node: CanvasNode = get_parent().get_node("Canvas Node")
@onready var pin_scene = preload("res://classes/pin 2d/pin_2d.tscn")

func _ready():
	# create a copy of input and output pins
	input_pins = input_pins.duplicate()
	output_pins = output_pins.duplicate()

	# set the display name for the gate
	label.text = gate_name

	# set the size of the color rect, which is the body of the gate, based on the number of input and output pins
	color_rect.size.x = label.get_minimum_size().x + 100
	color_rect.size.y = max(input_pins.size() + 1, output_pins.size() + 1) * distance_btw_pins
	color_rect.position.x = (color_rect.size.x - 40) / -2
	color_rect.position.y = (color_rect.size.y - distance_btw_pins - 5) / -2

	# set the size of the collision shape to the size of the color rect
	$CollisionShape2D.shape.size = color_rect.size - Vector2.ONE * 50

	# update the horizontal position of the input and output pins
	input_pin_container.position.x = ((color_rect.size.x - 40) / -2) - 30
	input_pin_container.position = canvas_node.snap_to_grid(input_pin_container.position)
	output_pin_container.position.x = ((color_rect.size.x - 40) / 2) + 30
	output_pin_container.position = canvas_node.snap_to_grid(output_pin_container.position)

	# create the input pins
	var pos_shift_input = floor(float(input_pins.size()) / 2)
	for i in range(input_pins.size()):
		var pin: Pin2D = pin_scene.instantiate()
		pin.is_input_pin = true
		pin.name = input_pins.keys()[i]
		if input_pins.size() % 2 == 0 and i >= pos_shift_input:
			pin.position.y = (i-pos_shift_input+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_input) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		pin.get_node("Right").text = input_pins.keys()[i]
		pin.get_node("Left").text = ""
		input_pin_container.add_child(pin)

	# create the output pins
	var pos_shift_output = floor(float(output_pins.size()) / 2)
	for i in range(output_pins.size()):
		var pin: Pin2D = pin_scene.instantiate()
		pin.name = output_pins.keys()[i]
		if output_pins.size() % 2 == 0 and i >= pos_shift_output:
			pin.position.y = (i-pos_shift_output+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_output) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		pin.get_node("Left").text = output_pins.keys()[i]
		pin.get_node("Right").text = ""
		output_pin_container.add_child(pin)
#endregion

#region Evaluate Expression
var current_output_for_each_pin: Dictionary[String, bool]

func evaluate_expression():
	var res = {}

	# evaluate the boolean expression for each output pin
	for pin in output_pin_container.get_children():
		var expr = Expression.new()
		input_pins["g"] = current_output_for_each_pin.get(pin.name, false)
		var err = expr.parse(boolean_expressions[pin.name], input_pins.keys())
		if err != OK:
			print("Error parsing expression: ", err)
			return false
		var res_i = expr.execute(input_pins.values())
		current_output_for_each_pin[pin.name] = res_i
		pin.state = res_i
		pin.transfer_state()
		res[pin.name] = res_i

	return res
#endregion

#region Generate Expression
# generates a complete boolean expression for the entire canvas (to be saved and made into custom gate)
func generate_complete_boolean_expression(gate_to_preserve: Gate2D = self) -> Dictionary:
	var complete_boolean_expressions = {}
	for boolean_expression in output_pins.keys():
		var curr_expression = boolean_expressions.get(boolean_expression, "")

		# separate the expression into individual terms
		var separated_expression = curr_expression.split(" ")

		# replace each input pin with the complete boolean expression of the gate that is connected to it
		for pin in input_pin_container.get_children():
			if len(pin.get_connected_wires()) > 0 and separated_expression.has(pin.name):
				var i = separated_expression.find(pin.name)
				var gate = pin.get_connected_wires()[0].input_pin.get_parent().get_parent()

				# if the gate is a switch, replace the pin name with the switch's name
				if gate is Switch:
					separated_expression[i] = str(gate.name)
				# if the gate is the one we are preserving, replace the pin name with "g" (for feedback loops)
				elif gate == gate_to_preserve:
					separated_expression[i] = "g"
				# otherwise, replace the pin name with the complete boolean expression of the pin of the gate
				elif gate is Gate2D:
					var output_pin_name = pin.get_connected_wires()[0].input_pin.name
					separated_expression[i] = "(%s)" % gate.generate_complete_boolean_expression(gate_to_preserve)[output_pin_name]
		curr_expression = " ".join(separated_expression)
		complete_boolean_expressions[boolean_expression] = curr_expression
	return complete_boolean_expressions
#endregion
