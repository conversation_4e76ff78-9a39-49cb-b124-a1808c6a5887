class_name Gate2D extends StaticBody2D

#region Render Gate
@export_group("General Gate Information")
@export var gate_name: String = "Gate"
@export var input_pins: Dictionary[String, bool]
@export var output_pins: Dictionary[String, bool]
@export var boolean_expressions: Dictionary

@export_group("Rendering")
@export var distance_btw_pins: int = 32
@export var gate_color: Color

@onready var input_pin_container = $"input pins"
@onready var output_pin_container = $"output pins"
@onready var label = $Control/Label
@onready var color_rect = $"Control/PanelContainer"
@onready var canvas_node: CanvasNode = get_parent().get_node("Canvas Node")
@onready var pin_scene = preload("res://classes/pin 2d/pin_2d.tscn")

func _ready():
	# create a copy of input and output pins
	input_pins = input_pins.duplicate()
	output_pins = output_pins.duplicate()

	# set the display name for the gate
	label.text = gate_name

	# set the size of the color rect, which is the body of the gate, based on the number of input and output pins
	color_rect.size.x = max(label.get_minimum_size().x, 100)
	color_rect.size.y = max(input_pins.size() + 1, output_pins.size() + 1) * distance_btw_pins
	color_rect.position.x = (color_rect.size.x - 40) / -2
	color_rect.position.y = (color_rect.size.y - distance_btw_pins - 5) / -2

	# set the size of the collision shape to the size of the color rect
	$CollisionShape2D.shape.size = color_rect.size - Vector2.ONE * 50

	# update the color of the color rect
	color_rect.modulate = gate_color

	# update the horizontal position of the input and output pins
	input_pin_container.position.x = color_rect.position.x - 30
	input_pin_container.position = canvas_node.snap_to_grid(input_pin_container.position)
	output_pin_container.position.x = color_rect.position.x + color_rect.size.x
	output_pin_container.position = canvas_node.snap_to_grid(output_pin_container.position)

	# create the input pins
	var pos_shift_input = floor(float(input_pins.size()) / 2)
	for i in range(input_pins.size()):
		# create the pin and position it accordingly
		var pin: Pin2D = pin_scene.instantiate()
		pin.is_input_pin = true
		pin.name = input_pins.keys()[i]
		if input_pins.size() % 2 == 0 and i >= pos_shift_input:
			pin.position.y = (i-pos_shift_input+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_input) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		input_pin_container.add_child(pin)

		# create the label for the pin and position it accordingly
		var new_label: Label = Label.new()
		new_label.text = input_pins.keys()[i]
		new_label.position = Vector2(-color_rect.size.x / 2 - 20, pin.position.y - 8)
		new_label.theme = load("res://theme/default theme.tres")
		new_label.set("theme_override_font_sizes/font_size", 10)
		new_label.modulate = Color.BLACK
		add_child(new_label)

		# connect the pin to the label with a Line2D
		(pin.get_node("Line2D") as Line2D).add_point(Vector2.RIGHT * 30)

	# create the output pins
	var pos_shift_output = floor(float(output_pins.size()) / 2)
	for i in range(output_pins.size()):
		# create the pin and position it accordingly
		var pin: Pin2D = pin_scene.instantiate()
		pin.name = output_pins.keys()[i]
		if output_pins.size() % 2 == 0 and i >= pos_shift_output:
			pin.position.y = (i-pos_shift_output+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_output) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		output_pin_container.add_child(pin)

		# create the label for the pin and position it accordingly
		var new_label: Label = Label.new()
		new_label.text = output_pins.keys()[i]
		new_label.position = Vector2(color_rect.size.x / 2 - 55, pin.position.y - 8)
		new_label.theme = load("res://theme/default theme.tres")
		new_label.set("theme_override_font_sizes/font_size", 10)
		new_label.modulate = Color.BLACK
		add_child(new_label)

		# connect the pin to the label with a Line2D
		(pin.get_node("Line2D") as Line2D).add_point(Vector2.LEFT * 30)
#endregion

#region Evaluate Expression
var current_output_for_each_pin: Dictionary[String, bool]
var last_clk_state = false

func evaluate_expression():
	var res = {}

	var can_evaluate_expression = not _has_clock()

	if _has_clock():
		can_evaluate_expression = _is_positive_edge_triggered()
		last_clk_state = input_pins.get("clk", false)

	if can_evaluate_expression:
		# evaluate the boolean expression for each output pin
		for pin in output_pin_container.get_children():
			var expr = Expression.new()

			# Prepare variables for expression evaluation
			var variables = input_pins.duplicate()

			# Add legacy "g" variable for backward compatibility
			variables["g"] = current_output_for_each_pin.get(pin.name, false)

			# Add cycle variables (for future use with improved cycle detection)
			# This allows expressions to reference previous states of gates in feedback loops
			var cycle_vars = _get_cycle_variables_for_evaluation()
			for var_name in cycle_vars.keys():
				variables[var_name] = cycle_vars[var_name]

			var err = expr.parse(boolean_expressions[pin.name], variables.keys())
			if err != OK:
				print("Error parsing expression: ", err)
				return false
			var res_i = expr.execute(variables.values())
			current_output_for_each_pin[pin.name] = res_i
			pin.state = res_i
			await pin.transfer_state()
			res[pin.name] = res_i

	return res

# Get cycle variables for expression evaluation (placeholder for future enhancement)
func _get_cycle_variables_for_evaluation() -> Dictionary:
	var cycle_vars = {}
	# This function can be extended to handle complex cycle variable resolution
	# For now, it returns an empty dictionary to maintain compatibility
	return cycle_vars

func _has_clock():
	return input_pins.has("clk")

func _is_positive_edge_triggered():
	return input_pins.get("clk", false) and not last_clk_state
#endregion

#region Generate Expression
# generates a complete boolean expression for the entire canvas (to be saved and made into custom gate)
func generate_complete_boolean_expression(gate_to_preserve: Gate2D = self) -> Dictionary:
	return _generate_expression_recursive(gate_to_preserve, [])

# Recursive expression generation with cycle detection using call stack
func _generate_expression_recursive(gate_to_preserve: Gate2D, call_stack: Array) -> Dictionary:
	var complete_boolean_expressions = {}

	# Add this gate to the call stack for cycle detection
	call_stack.append(self)

	for output_pin_name in output_pins.keys():
		var curr_expression = boolean_expressions.get(output_pin_name, "")
		if curr_expression.is_empty():
			complete_boolean_expressions[output_pin_name] = "false"
			continue

		# Process the expression by replacing input pin names
		var processed_expression = _replace_pins_in_expression(curr_expression, gate_to_preserve, call_stack)
		complete_boolean_expressions[output_pin_name] = processed_expression

	# Remove this gate from the call stack
	call_stack.pop_back()
	return complete_boolean_expressions

# Replace input pin names with their corresponding expressions
func _replace_pins_in_expression(expression: String, gate_to_preserve: Gate2D, call_stack: Array) -> String:
	var result = expression

	# Split expression into tokens (improved tokenization)
	var tokens = _tokenize_expression(expression)

	# Replace each input pin token
	for pin in input_pin_container.get_children():
		var pin_name = pin.name

		# Check if this pin name exists as a token
		if pin_name in tokens and len(pin.get_connected_wires()) > 0:
			var connected_wire = pin.get_connected_wires()[0]
			var source_gate = connected_wire.input_pin.get_parent().get_parent()
			var source_pin_name = connected_wire.input_pin.name

			var replacement = _get_replacement_for_pin(source_gate, source_pin_name, gate_to_preserve, call_stack)
			var result_separated = result.split(" ")
			for i in range(result_separated.size()):
				if result_separated[i] == pin_name:
					result_separated[i] = replacement
			result = " ".join(result_separated)

	return result

# Tokenize expression to identify variable names
func _tokenize_expression(expression: String) -> Array:
	var tokens = []

	for i in expression.split(" "):
		if not i in ["and", "or", "not", "(", ")", ""]:
			tokens.append(i)
	
	return tokens

# Get replacement string for a connected pin
func _get_replacement_for_pin(source_gate, source_pin_name: String, gate_to_preserve: Gate2D, call_stack: Array) -> String:
	# Handle switch inputs
	if source_gate is Switch:
		return str(source_gate.name)
	
	if source_gate is CLK:
		return "clk"

	# Handle Gate2D inputs
	if source_gate is Gate2D:
		# Check for cycles using call stack
		if source_gate in call_stack:
			# Cycle detected - use "g" for backward compatibility
			return "g"

		# No cycle - recursively get the expression
		var source_expressions = source_gate._generate_expression_recursive(gate_to_preserve, call_stack)
		return " ( " + source_expressions.get(source_pin_name, "false") + " ) "

	# Fallback for unknown gate types
	return "false"
#endregion
