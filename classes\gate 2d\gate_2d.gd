class_name Gate2D extends StaticBody2D

#region Render Gate
@export_group("General Gate Information")
@export var gate_name: String = "Gate"
@export var input_pins: Dictionary[String, bool]
@export var output_pins: Dictionary[String, bool]
@export var boolean_expressions: Dictionary

@export_group("Rendering")
@export var distance_btw_pins: int = 32

@onready var input_pin_container = $"input pins"
@onready var output_pin_container = $"output pins"
@onready var label = $Control/Label
@onready var color_rect = $"Control/PanelContainer"
@onready var canvas_node: CanvasNode = get_parent().get_node("Canvas Node")
@onready var pin_scene = preload("res://classes/pin 2d/pin_2d.tscn")

func _ready():
	# create a copy of input and output pins
	input_pins = input_pins.duplicate()
	output_pins = output_pins.duplicate()

	# set the display name for the gate
	label.text = gate_name

	# set the size of the color rect, which is the body of the gate, based on the number of input and output pins
	color_rect.size.x = label.get_minimum_size().x + 100
	color_rect.size.y = max(input_pins.size() + 1, output_pins.size() + 1) * distance_btw_pins
	color_rect.position.x = (color_rect.size.x - 40) / -2
	color_rect.position.y = (color_rect.size.y - distance_btw_pins - 5) / -2

	# set the size of the collision shape to the size of the color rect
	$CollisionShape2D.shape.size = color_rect.size - Vector2.ONE * 50

	# update the horizontal position of the input and output pins
	input_pin_container.position.x = ((color_rect.size.x - 40) / -2) - 30
	input_pin_container.position = canvas_node.snap_to_grid(input_pin_container.position)
	output_pin_container.position.x = ((color_rect.size.x - 40) / 2) + 30
	output_pin_container.position = canvas_node.snap_to_grid(output_pin_container.position)

	# create the input pins
	var pos_shift_input = floor(float(input_pins.size()) / 2)
	for i in range(input_pins.size()):
		var pin: Pin2D = pin_scene.instantiate()
		pin.is_input_pin = true
		pin.name = input_pins.keys()[i]
		if input_pins.size() % 2 == 0 and i >= pos_shift_input:
			pin.position.y = (i-pos_shift_input+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_input) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		pin.get_node("Right").text = input_pins.keys()[i]
		pin.get_node("Left").text = ""
		input_pin_container.add_child(pin)

	# create the output pins
	var pos_shift_output = floor(float(output_pins.size()) / 2)
	for i in range(output_pins.size()):
		var pin: Pin2D = pin_scene.instantiate()
		pin.name = output_pins.keys()[i]
		if output_pins.size() % 2 == 0 and i >= pos_shift_output:
			pin.position.y = (i-pos_shift_output+1) * distance_btw_pins
		else:
			pin.position.y = (i-pos_shift_output) * distance_btw_pins
		pin.position = canvas_node.snap_to_grid(pin.position)
		pin.get_node("Left").text = output_pins.keys()[i]
		pin.get_node("Right").text = ""
		output_pin_container.add_child(pin)
#endregion

#region Evaluate Expression
var current_output_for_each_pin: Dictionary[String, bool]

func evaluate_expression():
	var res = {}

	# evaluate the boolean expression for each output pin
	for pin in output_pin_container.get_children():
		var expr = Expression.new()

		# Prepare variables for expression evaluation
		var variables = input_pins.duplicate()

		# Add legacy "g" variable for backward compatibility
		variables["g"] = current_output_for_each_pin.get(pin.name, false)

		# Add cycle variables (for future use with improved cycle detection)
		# This allows expressions to reference previous states of gates in feedback loops
		var cycle_vars = _get_cycle_variables_for_evaluation()
		for var_name in cycle_vars.keys():
			variables[var_name] = cycle_vars[var_name]

		print(boolean_expressions)

		var err = expr.parse(boolean_expressions[pin.name], variables.keys())
		if err != OK:
			print("Error parsing expression: ", err)
			return false
		var res_i = expr.execute(variables.values())
		current_output_for_each_pin[pin.name] = res_i
		pin.state = res_i
		pin.transfer_state()
		res[pin.name] = res_i

	return res

# Get cycle variables for expression evaluation (placeholder for future enhancement)
func _get_cycle_variables_for_evaluation() -> Dictionary:
	var cycle_vars = {}
	# This function can be extended to handle complex cycle variable resolution
	# For now, it returns an empty dictionary to maintain compatibility
	return cycle_vars
#endregion

#region Generate Expression
# generates a complete boolean expression for the entire canvas (to be saved and made into custom gate)
func generate_complete_boolean_expression(gate_to_preserve: Gate2D = self) -> Dictionary:
	# Use improved cycle-aware expression generation
	var visited_gates = {}
	var cycle_variables = {}
	return _generate_expression_with_cycle_detection(gate_to_preserve, visited_gates, cycle_variables)

# Improved expression generation with proper cycle detection
func _generate_expression_with_cycle_detection(gate_to_preserve: Gate2D, visited_gates: Dictionary, cycle_variables: Dictionary) -> Dictionary:
	var complete_boolean_expressions = {}

	# Mark this gate as being processed
	var gate_id = str(get_instance_id())
	visited_gates[gate_id] = "processing"

	for output_pin_name in output_pins.keys():
		var curr_expression = boolean_expressions.get(output_pin_name, "")
		if curr_expression.is_empty():
			complete_boolean_expressions[output_pin_name] = "false"
			continue

		# Use regex-based token replacement for more robust parsing
		var processed_expression = _replace_tokens_in_expression(curr_expression, gate_to_preserve, visited_gates, cycle_variables)
		complete_boolean_expressions[output_pin_name] = processed_expression

	# Mark this gate as completed
	visited_gates[gate_id] = "completed"
	return complete_boolean_expressions

# Replace input pin tokens with their corresponding expressions
func _replace_tokens_in_expression(expression: String, gate_to_preserve: Gate2D, visited_gates: Dictionary, cycle_variables: Dictionary) -> String:
	var result = expression

	# Find all input pins and their connections
	for pin in input_pin_container.get_children():
		var pin_name = pin.name

		# Use word boundary regex to match exact pin names
		var regex = RegEx.new()
		regex.compile("\\b" + pin_name + "\\b")

		if regex.search(result) and len(pin.get_connected_wires()) > 0:
			var connected_wire = pin.get_connected_wires()[0]
			var source_gate = connected_wire.input_pin.get_parent().get_parent()
			var source_pin_name = connected_wire.input_pin.name

			var replacement = _get_replacement_for_connection(source_gate, source_pin_name, gate_to_preserve, visited_gates, cycle_variables)
			result = regex.sub(result, replacement, true)

	return result

# Get the appropriate replacement string for a connection
func _get_replacement_for_connection(source_gate, source_pin_name: String, gate_to_preserve: Gate2D, visited_gates: Dictionary, cycle_variables: Dictionary) -> String:
	# Handle switch inputs
	if source_gate is Switch:
		return str(source_gate.name)

	# Handle Gate2D inputs
	if source_gate is Gate2D:
		var source_gate_id = str(source_gate.get_instance_id())

		# Check for cycles
		if visited_gates.has(source_gate_id) and visited_gates[source_gate_id] == "processing":
			# We found a cycle - create a cycle variable
			var cycle_var_name = "cycle_" + source_gate_id + "_" + source_pin_name
			cycle_variables[cycle_var_name] = {
				"gate": source_gate,
				"pin": source_pin_name
			}
			return cycle_var_name

		# No cycle detected - recursively get the expression
		var source_expressions = source_gate._generate_expression_with_cycle_detection(gate_to_preserve, visited_gates, cycle_variables)
		return "(" + source_expressions.get(source_pin_name, "false") + ")"

	# Fallback for unknown gate types
	return "false"
#endregion
