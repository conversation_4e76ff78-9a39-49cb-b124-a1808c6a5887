; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Digital Electronics Simulator"
run/main_scene="uid://c17gwt0a7qnpp"
config/features=PackedStringArray("4.4", "Mobile")
config/icon="res://icon.svg"

[autoload]

SimulationManager="*res://simulation manager/SimulationManager.gd"
SaveSystem="*res://save and load/save system.gd"

[file_customization]

folder_colors={
"res://built in gates/": "red",
"res://built in gates/1-4 DEMUX/": "yellow",
"res://built in gates/3-PIN AND/": "orange",
"res://built in gates/4 BIT UP COUNTER/": "yellow",
"res://built in gates/4-1 MUX/": "yellow",
"res://built in gates/4-BIT PIPO REGISTER/": "yellow",
"res://built in gates/AND/": "orange",
"res://built in gates/CLK/": "orange",
"res://built in gates/D Flip Flop/": "yellow",
"res://built in gates/JK Flip Flop/": "yellow",
"res://built in gates/NAND/": "orange",
"res://built in gates/NOR/": "orange",
"res://built in gates/NOT/": "orange",
"res://built in gates/OR/": "orange",
"res://built in gates/SR Flip Flop/": "yellow",
"res://built in gates/SR Latch/": "yellow",
"res://built in gates/T Flip Flop/": "yellow",
"res://built in gates/XNOR/": "orange",
"res://built in gates/XOR/": "orange",
"res://built in gates/output/": "orange",
"res://built in gates/switch/": "orange",
"res://canvas node/": "green",
"res://classes/": "teal",
"res://fonts/": "blue",
"res://gate library/": "purple",
"res://save and load/": "pink"
}

[global_group]

pin2d=""
switch=""
output=""
gate2d=""
clk=""
special_gates=""

[rendering]

renderer/rendering_method="mobile"
environment/defaults/default_clear_color=Color(0.0142752, 0.0142752, 0.0142752, 1)
