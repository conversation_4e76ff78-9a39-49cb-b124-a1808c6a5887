class_name CanvasNode extends Node2D

@onready var detector: Area2D = $"Detector"
var can_interact = true

#region Grid Drawing
@export_group("Grid Drawing")
@export var grid_size := 32
@export var dot_radius := 1.5
@export var dot_color: Color
@onready var cam := $Camera2D

func _draw():
	var device_dim = DisplayServer.window_get_size()
	var top_left = Vector2i(cam.position) - device_dim
	var bottom_right = Vector2i(cam.position) + device_dim * 2

	# snap top left and bottom right to the grid
	var start_x = floor(top_left.x/grid_size) * grid_size
	var end_x = ceil(bottom_right.x/grid_size) * grid_size
	var start_y = floor(top_left.y/grid_size) * grid_size
	var end_y = ceil(bottom_right.y/grid_size) * grid_size

	# starting from the snapped top left to the snapped bottom right, draw a circle in every "grid_size"
	for x in range(start_x, end_x + 1, grid_size):
		for y in range(start_y, end_y + 1, grid_size):
			draw_circle(Vector2(x, y), dot_radius, dot_color)

func _ready():
	queue_redraw()

#endregion

#region Camera Motion
@export_group("Camera Motion")
@export var pan_sensitivity := 1.0
@export var zoom_sensitivity := 1.0
@export var max_zoom := 3.0

var is_panning := false
var last_mouse_position := Vector2.ZERO

func _process_camera_motion_input(event):
	if is_dragging_gate:
		is_panning = false
		return

	# override camera motion if dragging gate
	if event is InputEventMouseButton:
		# if left click, start panning
		if event.button_index == MOUSE_BUTTON_LEFT:
			is_panning = event.pressed
			last_mouse_position = event.position

			# if released left click, redraw the grid
			if not event.pressed:
				queue_redraw()

		# handle zoom
		var tween = get_tree().create_tween().set_trans(Tween.TRANS_LINEAR).set_ease(Tween.EASE_IN_OUT)
		var new_zoom = cam.zoom

		# set new zoom based on zoom in
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			new_zoom += Vector2.ONE * zoom_sensitivity
			new_zoom.x = min(new_zoom.x, max_zoom)
			new_zoom.y = min(new_zoom.y, max_zoom)

		# set new zoom based on zoom out
		if event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			new_zoom -= Vector2.ONE * zoom_sensitivity
			new_zoom.x = max(new_zoom.x, 1)
			new_zoom.y = max(new_zoom.y, 1)
		
		# animate smooth zooming
		tween.tween_property(cam, "zoom", new_zoom, 0.2)
	elif event is InputEventMouseMotion and is_panning:
		# if mouse is moving and panning started, update camera position
		var delta = last_mouse_position - event.position
		cam.position += delta * pan_sensitivity
		last_mouse_position = event.position
#endregion

#region Gate Dragging
@export_group("Gate Dragging")
@export var gate_dragged_size := 1.2

var is_dragging_gate = false
var dragged_gate = null


func _process_gate_dragging_input(event):
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			if event.pressed:
				# if the mouse is over a gate and is left clicked, start dragging
				for body in detector.get_overlapping_bodies():
					if body is Gate2D or body is Switch or body is Output:
						dragged_gate = body

						# animate the gate to be larger
						var tween = get_tree().create_tween().set_trans(Tween.TRANS_BOUNCE).set_ease(Tween.EASE_OUT)
						tween.tween_property(dragged_gate, "scale", Vector2.ONE * gate_dragged_size, 0.1)

						is_dragging_gate = event.pressed
						break
			# if left click is released, place the gate in the grid
			else:
				# TODO: check if gate is over the delete region
				await _place_gate_in_grid()
				dragged_gate = null
				is_dragging_gate = false
	elif event is InputEventMouseMotion:
		# update the gate position if is dragging
		detector.global_position = cam.get_global_mouse_position()
		if dragged_gate:
			var tween = get_tree().create_tween().set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
			tween.tween_property(dragged_gate, "global_position", detector.global_position, 0.3)

func _place_gate_in_grid():
	if dragged_gate:
		var tween = get_tree().create_tween().set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
		tween.tween_property(dragged_gate, "global_position", snap_to_grid(dragged_gate.global_position), 0.3)
		var tween2 = get_tree().create_tween().set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
		tween2.tween_property(dragged_gate, "scale", Vector2.ONE, 0.3)
#endregion

#region Public Functions
func snap_to_grid(pos):
	var left_x = floor(pos.x/grid_size) * grid_size
	var right_x = ceil(pos.x/grid_size) * grid_size
	var top_y = floor(pos.y/grid_size) * grid_size
	var bottom_y = ceil(pos.y/grid_size) * grid_size
	var closest_x = left_x
	var closest_y = top_y
	if pos.x - left_x < right_x - pos.x:
		closest_x = left_x
	else:
		closest_x = right_x
	if pos.y - top_y < bottom_y - pos.y:
		closest_y = top_y
	else:
		closest_y = bottom_y
	return Vector2(
		closest_x,
		closest_y
	)

func get_snapped_detector_position():
	return snap_to_grid(detector.global_position)
#endregion

#region System Functions
func _input(event):
	if not can_interact: return
	_process_camera_motion_input(event)
	_process_gate_dragging_input(event)
#endregion
