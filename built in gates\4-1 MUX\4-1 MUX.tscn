[gd_scene load_steps=5 format=3 uid="uid://cfefbidh03sbu"]

[ext_resource type="Script" uid="uid://qea55q2wbs4n" path="res://classes/gate 2d/gate_2d.gd" id="1_45f4s"]
[ext_resource type="Theme" uid="uid://bhsrstq1b653s" path="res://theme/default theme.tres" id="2_atm0y"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mtvlf"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="RectangleShape2D" id="RectangleShape2D_4tbfx"]
size = Vector2(50, 50)

[node name="4-1 MUX" type="StaticBody2D"]
script = ExtResource("1_45f4s")
gate_name = "4 TO 1 MUX"
input_pins = Dictionary[String, bool]({
"d1": false,
"d2": false,
"d3": false,
"d4": false,
"s1": false,
"s2": false
})
output_pins = Dictionary[String, bool]({
"q": false
})
boolean_expressions = {
"q": " (  (  (  (  (  (  ( d1 ) and (  ( not s1 )  ) and (  ( not s2 )  )  )  ) or (  (  ( d2 ) and ( s1 ) and (  ( not s2 )  )  )  )  )  ) or (  (  ( d3 ) and (  ( not s1 )  ) and ( s2 )  )  )  )  ) or (  (  ( d4 ) and ( s1 ) and ( s2 )  )  ) "
}

[node name="input pins" type="Node2D" parent="."]

[node name="output pins" type="Node2D" parent="."]

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="PanelContainer" type="PanelContainer" parent="Control"]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0
theme_override_styles/panel = SubResource("StyleBoxFlat_mtvlf")

[node name="Label" type="Label" parent="Control"]
modulate = Color(0, 0, 0, 1)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -11.5
offset_right = 40.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_atm0y")
text = "AND"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_4tbfx")
