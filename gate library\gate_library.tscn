[gd_scene load_steps=26 format=3 uid="uid://bk51ijfjwcpop"]

[ext_resource type="Script" uid="uid://b5nlww4gdkuqb" path="res://gate library/gate_library.gd" id="1_3mjll"]
[ext_resource type="Texture2D" uid="uid://bd154keaa1up5" path="res://gate library/icons8-align-justify-96.png" id="2_bipk0"]
[ext_resource type="PackedScene" uid="uid://cbp5iuokt100t" path="res://built in gates/AND/AND.tscn" id="2_rgmpv"]
[ext_resource type="Theme" uid="uid://dpf0xnga20efv" path="res://theme/gate library theme.tres" id="3_rgmpv"]
[ext_resource type="PackedScene" uid="uid://ca10u85exq41l" path="res://built in gates/NOT/NOT.tscn" id="3_t6pcn"]
[ext_resource type="StyleBox" uid="uid://oui3wg68lehu" path="res://theme/icon button.tres" id="4_0s225"]
[ext_resource type="PackedScene" uid="uid://cw1idikcog0vr" path="res://built in gates/OR/OR.tscn" id="4_y1tvn"]
[ext_resource type="PackedScene" uid="uid://bdup3x3qhrj4b" path="res://built in gates/switch/switch.tscn" id="5_7uwcm"]
[ext_resource type="PackedScene" uid="uid://b72txgy5nkkuo" path="res://built in gates/XOR/XOR.tscn" id="6_y1tvn"]
[ext_resource type="PackedScene" uid="uid://b3yxk11krpyhx" path="res://built in gates/NAND/NAND.tscn" id="7_geyfm"]
[ext_resource type="PackedScene" uid="uid://dy50i15s41sb7" path="res://built in gates/NOR/NOR.tscn" id="8_wrnwk"]
[ext_resource type="PackedScene" uid="uid://chgq41wbs38nd" path="res://built in gates/SR Latch/SR LATCH.tscn" id="9_wrnwk"]
[ext_resource type="PackedScene" uid="uid://br8bwfdpj2fy7" path="res://built in gates/output/output.tscn" id="10_aii5t"]
[ext_resource type="FontFile" uid="uid://dbbbj2vjgqp1d" path="res://fonts/Poppins/Poppins-Black.ttf" id="13_f1jsb"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xy1hq"]
bg_color = Color(0.412152, 0.412152, 0.412152, 1)
corner_radius_top_left = 100
corner_radius_top_right = 100
corner_radius_bottom_right = 100
corner_radius_bottom_left = 100
expand_margin_left = 10.0
expand_margin_top = 10.0
expand_margin_right = 10.0
expand_margin_bottom = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0s225"]
bg_color = Color(0.0768358, 0.0768358, 0.0768358, 1)
corner_radius_top_left = 100
corner_radius_top_right = 100
corner_radius_bottom_right = 100
corner_radius_bottom_left = 100
expand_margin_left = 10.0
expand_margin_top = 10.0
expand_margin_right = 10.0
expand_margin_bottom = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_wrnwk"]
content_margin_left = 100.0
content_margin_top = 100.0
content_margin_right = 100.0
content_margin_bottom = 100.0
bg_color = Color(0.1722, 0.41, 0.366403, 1)
corner_radius_top_left = 50
corner_radius_top_right = 50
corner_radius_bottom_right = 50
corner_radius_bottom_left = 50

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_f1jsb"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f1jsb"]
bg_color = Color(0.0849781, 0.235587, 0.206094, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_aii5t"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_aii5t"]
content_margin_left = 20.0
content_margin_top = 10.0
content_margin_right = 20.0
content_margin_bottom = 10.0
bg_color = Color(0.226707, 0.226708, 0.226707, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kmpg4"]
content_margin_left = 20.0
content_margin_top = 10.0
content_margin_right = 20.0
content_margin_bottom = 10.0
bg_color = Color(0.776471, 0.219608, 0.219608, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="Animation" id="Animation_aii5t"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Save Gate/PanelContainer:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.2, 0.2)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Save Gate/PanelContainer:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_f1jsb"]
resource_name = "show save menu"
length = 0.7
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Save Gate/PanelContainer:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.266667, 0.633333),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.2, 0.2), Vector2(1, 1), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Save Gate/PanelContainer:modulate")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.266667, 0.633333),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_aii5t"]
_data = {
&"RESET": SubResource("Animation_aii5t"),
&"show save menu": SubResource("Animation_f1jsb")
}

[node name="Gate Library" type="CanvasLayer"]
script = ExtResource("1_3mjll")
basic_gates = Array[PackedScene]([ExtResource("2_rgmpv"), ExtResource("3_t6pcn"), ExtResource("4_y1tvn"), ExtResource("5_7uwcm"), ExtResource("6_y1tvn"), ExtResource("7_geyfm"), ExtResource("8_wrnwk"), ExtResource("9_wrnwk"), ExtResource("10_aii5t")])

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="open gate library button" type="Button" parent="Control"]
layout_mode = 1
offset_left = 29.0
offset_top = 24.0
offset_right = 58.0
offset_bottom = 53.0
theme = ExtResource("3_rgmpv")
theme_override_styles/focus = ExtResource("4_0s225")
theme_override_styles/hover = SubResource("StyleBoxFlat_xy1hq")
theme_override_styles/pressed = SubResource("StyleBoxFlat_0s225")
theme_override_styles/normal = ExtResource("4_0s225")
icon = ExtResource("2_bipk0")
icon_alignment = 1
expand_icon = true

[node name="Control" type="Control" parent="Control"]
layout_mode = 1
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -399.0
grow_vertical = 2
theme = ExtResource("3_rgmpv")

[node name="TabContainer" type="TabContainer" parent="Control/Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -2.0
grow_horizontal = 2
grow_vertical = 2
current_tab = 2

[node name="Basic" type="ScrollContainer" parent="Control/Control/TabContainer"]
visible = false
layout_mode = 2
metadata/_tab_index = 0

[node name="Basic" type="VBoxContainer" parent="Control/Control/TabContainer/Basic"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
alignment = 1

[node name="Advanced" type="ScrollContainer" parent="Control/Control/TabContainer"]
visible = false
layout_mode = 2
metadata/_tab_index = 1

[node name="Advanced" type="VBoxContainer" parent="Control/Control/TabContainer/Advanced"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
alignment = 1

[node name="Custom" type="ScrollContainer" parent="Control/Control/TabContainer"]
layout_mode = 2
metadata/_tab_index = 2

[node name="Custom" type="VBoxContainer" parent="Control/Control/TabContainer/Custom"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
alignment = 1

[node name="Save Gate" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(602, 335)
mouse_filter = 2

[node name="Save Gate Button" type="Button" parent="Save Gate"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -85.0
offset_bottom = 31.0
grow_horizontal = 0
text = "Save Gate"

[node name="PanelContainer" type="PanelContainer" parent="Save Gate"]
modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 167.0
offset_top = 94.0
offset_right = -167.0
offset_bottom = -94.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.2, 0.2)
pivot_offset = Vector2(415, 231)
theme_override_styles/panel = SubResource("StyleBoxFlat_wrnwk")

[node name="VBoxContainer" type="VBoxContainer" parent="Save Gate/PanelContainer"]
layout_mode = 2
theme_override_constants/separation = 20
alignment = 1

[node name="LineEdit" type="LineEdit" parent="Save Gate/PanelContainer/VBoxContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("13_f1jsb")
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_f1jsb")
theme_override_styles/normal = SubResource("StyleBoxFlat_f1jsb")
placeholder_text = "Name Of Gate"
alignment = 1

[node name="HBoxContainer2" type="HBoxContainer" parent="Save Gate/PanelContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 50
alignment = 1

[node name="HBoxContainer" type="HBoxContainer" parent="Save Gate/PanelContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2

[node name="Label" type="Label" parent="Save Gate/PanelContainer/VBoxContainer/HBoxContainer2/HBoxContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("13_f1jsb")
theme_override_font_sizes/font_size = 10
text = "Color"

[node name="ColorPickerButton" type="ColorPickerButton" parent="Save Gate/PanelContainer/VBoxContainer/HBoxContainer2/HBoxContainer"]
layout_mode = 2
text = "ccc"
color = Color(0.473976, 0.473976, 0.473976, 1)
edit_alpha = false

[node name="HBoxContainer" type="HBoxContainer" parent="Save Gate/PanelContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 50
alignment = 1

[node name="Confirm Safe Button" type="Button" parent="Save Gate/PanelContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("13_f1jsb")
theme_override_font_sizes/font_size = 20
theme_override_styles/hover = SubResource("StyleBoxEmpty_aii5t")
theme_override_styles/normal = SubResource("StyleBoxFlat_aii5t")
text = "Save"

[node name="Back" type="Button" parent="Save Gate/PanelContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("13_f1jsb")
theme_override_font_sizes/font_size = 20
theme_override_styles/hover = SubResource("StyleBoxEmpty_aii5t")
theme_override_styles/normal = SubResource("StyleBoxFlat_kmpg4")
text = "Back"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_aii5t")
}

[connection signal="button_down" from="Control/open gate library button" to="." method="_on_open_gate_library_button_button_down"]
[connection signal="pressed" from="Save Gate/Save Gate Button" to="." method="_on_save_gate_button_pressed"]
[connection signal="pressed" from="Save Gate/PanelContainer/VBoxContainer/HBoxContainer/Confirm Safe Button" to="." method="_on_confirm_safe_button_pressed"]
[connection signal="pressed" from="Save Gate/PanelContainer/VBoxContainer/HBoxContainer/Back" to="." method="_on_back_pressed"]
