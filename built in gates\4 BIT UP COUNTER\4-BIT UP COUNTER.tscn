[gd_scene load_steps=5 format=3 uid="uid://bhgdjihiy17b7"]

[ext_resource type="Script" uid="uid://qea55q2wbs4n" path="res://classes/gate 2d/gate_2d.gd" id="1_umyut"]
[ext_resource type="Theme" uid="uid://bhsrstq1b653s" path="res://theme/default theme.tres" id="2_r7abd"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mtvlf"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="RectangleShape2D" id="RectangleShape2D_4tbfx"]
size = Vector2(50, 50)

[node name="4-BIT UP COUNTER" type="StaticBody2D"]
script = ExtResource("1_umyut")
gate_name = "4-BIT UP COUNTER"
input_pins = Dictionary[String, bool]({
"clk": false
})
output_pins = Dictionary[String, bool]({
"q1": false,
"q2": false,
"q3": false,
"q4": false
})
boolean_expressions = {
"q1": "not ( ( not (  ( ( g ) and ( clk ) )  or g ) ) or  ( ( clk ) and (  ( not g )  ) )  )",
"q2": "not ( ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or g ) ) or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  )",
"q3": "not ( ( not (  ( ( g ) and (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) )  or g ) ) or  ( (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  )",
"q4": "not ( ( not (  ( ( g ) and (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) )  or g ) ) or  ( (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) )  or ( not ( g or  ( (  ( not (  ( ( g ) and ( clk ) )  or ( not ( g or  ( ( clk ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  ) ) ) )  ) and (  ( not g )  ) )  )"
}

[node name="input pins" type="Node2D" parent="."]

[node name="output pins" type="Node2D" parent="."]

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="PanelContainer" type="PanelContainer" parent="Control"]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0
theme_override_styles/panel = SubResource("StyleBoxFlat_mtvlf")

[node name="Label" type="Label" parent="Control"]
modulate = Color(0, 0, 0, 1)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -11.5
offset_right = 40.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_r7abd")
text = "AND"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_4tbfx")
