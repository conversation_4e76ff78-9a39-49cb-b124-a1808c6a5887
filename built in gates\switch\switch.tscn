[gd_scene load_steps=6 format=3 uid="uid://bdup3x3qhrj4b"]

[ext_resource type="Script" uid="uid://cmeg7gh0l8glk" path="res://built in gates/switch/switch.gd" id="1_livvw"]
[ext_resource type="PackedScene" uid="uid://es2mohaxdhi" path="res://classes/pin 2d/pin_2d.tscn" id="1_pqxte"]
[ext_resource type="Theme" uid="uid://bhsrstq1b653s" path="res://theme/default theme.tres" id="3_6fbso"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_livvw"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_6fbso"]
radius = 18.0
height = 42.0

[node name="a" type="StaticBody2D" groups=["switch"]]
script = ExtResource("1_livvw")

[node name="output pins" type="Node2D" parent="."]

[node name="Pin 2D" parent="output pins" instance=ExtResource("1_pqxte")]
position = Vector2(32, 0)

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="PanelContainer" type="PanelContainer" parent="Control"]
modulate = Color(0.160784, 0.662745, 0.803922, 1)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -136.0
offset_top = -36.0
offset_right = 9.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_livvw")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_6fbso")

[node name="CheckButton" type="CheckButton" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -73.0
offset_top = -14.0
offset_right = -28.0
offset_bottom = 14.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("3_6fbso")

[node name="LineEdit" type="LineEdit" parent="."]
offset_left = -108.0
offset_top = -11.0
offset_right = -79.0
offset_bottom = 12.0
theme = ExtResource("3_6fbso")
text = "a"
alignment = 1

[node name="Label" type="Label" parent="."]
modulate = Color(0, 0, 0, 1)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -11.5
offset_right = 40.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("3_6fbso")
theme_override_font_sizes/font_size = 10
text = "switch"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="toggled" from="CheckButton" to="." method="_on_check_button_toggled"]
