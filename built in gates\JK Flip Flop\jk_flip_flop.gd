extends Gate2D

func custom_evaluate_expression():
	var res: Dictionary[String, bool] = {}

	var j = input_pins.get("j", false)
	var k = input_pins.get("k", false)

	var q = current_output_for_each_pin.get("q", false)

	if _is_positive_edge_triggered():
		if not j and not k:
			res["q"] = q
		elif j and not k:
			res["q"] = true
		elif not j and k:
			res["q"] = false
		elif j and k:
			res["q"] = not q

		res["qn"] = not res["q"]

		for pin in output_pin_container.get_children():
			if pin.name in res:
				pin.state = res[pin.name]
				await pin.transfer_state()
		
		current_output_for_each_pin = res
	return res
