extends Node

const SAVE_FILE_PATH = "user://custom_gates.json"

var custom_gates := {
	"custom_gates": []
}

func add_custom_gate_and_save(gate_data: Dictionary):
	load_data()
	custom_gates["custom_gates"].append(gate_data)
	save_data()

func save_data():
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if file:
		file.store_string(JSON.stringify(custom_gates))
		file.close()
		print("Data saved successfully")
		return true
	else:
		print("Failed to save game")
	return false

func load_data():
	if not FileAccess.file_exists(SAVE_FILE_PATH):
		print("No save file found, using default data")
		return false
	
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()

		var json = JSON.new()
		var parse_result = json.parse(json_string)

		if parse_result == OK:
			custom_gates = json.data
			print(custom_gates)
			print("Data loaded successfully")
			return true
		else:
			print("Failed to parse save file")
			return false
	else:
		print("Failed to open save file")
		return false

func delete_save():
	if FileAccess.file_exists(SAVE_FILE_PATH):
		DirAccess.remove_absolute(SAVE_FILE_PATH)
		print("Save file deleted")
		return true
	return false

func has_save_file() -> bool:
	return FileAccess.file_exists(SAVE_FILE_PATH)

# Call this when the game starts
func _init():
	load_data()
