class_name Wire2D extends Line2D

#region Construct Wire
@onready var canvas_node: CanvasNode = get_parent().get_node("Canvas Node")

@export_group("Rounded Corners")
@export var corner_radius: float = 10.0
@export var segments: int = 10

var input_pin: Pin2D
var output_pin: Pin2D
var selected = false
var state = false
var unrounded_points = []

func _input(event):
	if event is InputEventMouseButton:
		# if left clicked
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
			# if the output pin is not yet set (wire construction not completed)
			if not output_pin:
				# add a new point to the line based on mouse position
				add_point(canvas_node.get_snapped_detector_position())
				unrounded_points.append(canvas_node.get_snapped_detector_position())
				for body in canvas_node.detector.get_overlapping_bodies():
					# if the new point touches a new pin that is not itself
					if body is Pin2D and body != input_pin and SimulationManager.curr_active_wire == self:
						# set the pin as output pin
						output_pin = body
						output_pin.connected = true
						# generate collision shape (for wire deletion handling)
						_generate_collision_shape()
						points = _smoothed_corners(points, corner_radius, segments)
						await get_tree().create_timer(0.01).timeout
						SimulationManager.curr_active_wire = null
						SimulationManager.update_all_pins()
						break
			# if the wire construction is completed, but is still left clicked
			else:
				# check if the mouse is over the wire using the collision shape
				selected = canvas_node.detector.has_overlapping_areas()
				for body in canvas_node.detector.get_overlapping_areas():
					await get_tree().create_timer(0.01).timeout
					# if the mouse is over the wire
					if body.get_parent() == self and not SimulationManager.curr_active_wire:
						# show the delete button
						selected = true
						$Node2D.global_position = canvas_node.detector.global_position
						$Node2D/Button.visible = true
						$AnimationPlayer.play("reveal button")
						break
					else:
						selected = false
				# if the mouse is not over the wire, hide the delete button if the delete button has been shown previously
				if not selected and $Node2D/Button.visible:
					await get_tree().create_timer(0.2).timeout
					$AnimationPlayer.play_backwards("reveal button")
					$Node2D/Button.visible = false

func _smoothed_corners(old_points: Array, radius: float, arc_segments: int):
	if old_points.size() < 3:
		return old_points

	var new_points = []
	new_points.append(old_points[0])

	for i in range(1, old_points.size() - 1):
		var a = old_points[i - 1]
		var b = old_points[i]
		var c = old_points[i + 1]

		var ab = (b - a).normalized()
		var cb = (c - b).normalized()

		var angle = ab.angle_to(cb)

		if ab.dot(cb) != 0:
			# Skip near-straight angles
			new_points.append(b)
			continue

		# Trim radius from B along AB and CB
		var trim_a = b - ab * radius
		var trim_c = b + cb * radius  # cb points from B to C

		# Bisector-based method
		var bisector = (ab + -cb).normalized()
		var sin_half_angle = sin(abs(angle) / 2)
		var arc_center = b - (bisector * radius / sin_half_angle)

		# Arc angles
		var from_angle = (trim_a - arc_center).angle()
		var to_angle = (trim_c - arc_center).angle()

		# Normalize direction (always go shortest arc)
		var arc_points = []
		var total_angle = wrapf(to_angle - from_angle, -PI, PI)

		for j in range(arc_segments + 1):
			var t = j / float(arc_segments)
			var ang = from_angle + total_angle * t
			var p = arc_center + Vector2.RIGHT.rotated(ang) * radius
			arc_points.append(p)

		new_points.append(trim_a)
		new_points.append_array(arc_points)
		new_points.append(trim_c)

	new_points.append(old_points[old_points.size() - 1])
	return new_points

func _generate_collision_shape():
	for i in range(unrounded_points.size() - 1):
		var p1 = unrounded_points[i]
		var p2 = unrounded_points[i+1]
		var shape = SegmentShape2D.new()
		shape.a = p1
		shape.b = p2
		var collision_shape = CollisionShape2D.new()
		collision_shape.shape = shape
		$Area2D.add_child(collision_shape)
#endregion

#region Wire Color Management
@export_group("Colors")
@export var tracing_color: Color
@export var completed_color: Color
@export var selected_color: Color
@export var activated_color: Color

func _process(_delta):
	# get the new color based on wire state
	var new_color = default_color
	if not output_pin:
		new_color = tracing_color
	elif selected:
		new_color = selected_color
	elif state:
		new_color = activated_color
	else:
		new_color = completed_color

	# if the new color is different from the current color, tween the color
	if new_color != default_color:
		var tween = get_tree().create_tween()
		tween.tween_property(self, "default_color", new_color, 0.5)
#endregion

#region Wire Deletion
func _on_button_pressed():
	input_pin.connected = false
	output_pin.connected = false
	SimulationManager.update_all_pins()
	queue_free()
#endregion
