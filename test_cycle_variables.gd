extends Node

# Test script to verify cycle variable handling for D flip-flops and counters

func _ready():
	print("=== Testing Cycle Variable System ===")
	test_d_flip_flop_cycle_variables()
	test_counter_cycle_variables()
	test_cycle_variable_evaluation()

func test_d_flip_flop_cycle_variables():
	print("\n1. Testing D Flip-Flop Cycle Variables:")
	
	# Create a mock D flip-flop
	var dff = Gate2D.new()
	dff.gate_name = "D_FF_Test"
	dff.input_pins = {"clk": false, "d": false}
	dff.output_pins = {"q": false, "qn": false}
	dff.boolean_expressions = {
		"q": "d and clk or g and not clk",  # Simplified D flip-flop logic
		"qn": "not q"
	}
	
	# Set some previous state
	dff.current_output_for_each_pin = {"q": true, "qn": false}
	
	# Test cycle variable generation
	var cycle_vars = dff._get_cycle_variables_for_evaluation()
	
	print("  Generated cycle variables:")
	for var_name in cycle_vars.keys():
		print("    ", var_name, ": ", cycle_vars[var_name])
	
	# Verify expected variables exist
	var expected_vars = ["g", "prev_q", "prev_qn", "clk_signal"]
	var all_present = true
	for var_name in expected_vars:
		if not cycle_vars.has(var_name):
			print("  ❌ Missing expected variable: ", var_name)
			all_present = false
	
	if all_present:
		print("  ✅ All expected cycle variables present")
	
	dff.queue_free()

func test_counter_cycle_variables():
	print("\n2. Testing Counter Cycle Variables:")
	
	# Create a mock 2-bit counter (simplified)
	var counter = Gate2D.new()
	counter.gate_name = "2BIT_COUNTER"
	counter.input_pins = {"clk": false}
	counter.output_pins = {"q0": false, "q1": false}
	counter.boolean_expressions = {
		"q0": "not prev_q0 and clk",  # Toggle on clock
		"q1": "prev_q1 xor (prev_q0 and clk)"  # Toggle when q0 transitions
	}
	
	# Set some previous state
	counter.current_output_for_each_pin = {"q0": false, "q1": true}
	
	# Test cycle variable generation
	var cycle_vars = counter._get_cycle_variables_for_evaluation()
	
	print("  Generated cycle variables:")
	for var_name in cycle_vars.keys():
		print("    ", var_name, ": ", cycle_vars[var_name])
	
	# Test that prev_ variables have correct values
	if cycle_vars.get("prev_q0") == false and cycle_vars.get("prev_q1") == true:
		print("  ✅ Previous state variables correct")
	else:
		print("  ❌ Previous state variables incorrect")
	
	counter.queue_free()

func test_cycle_variable_evaluation():
	print("\n3. Testing Cycle Variable in Expression Evaluation:")
	
	# Create a simple toggle gate
	var toggle = Gate2D.new()
	toggle.gate_name = "TOGGLE"
	toggle.input_pins = {"trigger": false}
	toggle.output_pins = {"out": false}
	toggle.boolean_expressions = {"out": "trigger and not prev_out or not trigger and prev_out"}
	
	# Set initial state
	toggle.current_output_for_each_pin = {"out": false}
	toggle.input_pins["trigger"] = true
	
	print("  Initial state: out =", toggle.current_output_for_each_pin["out"])
	print("  Input: trigger =", toggle.input_pins["trigger"])
	print("  Expression:", toggle.boolean_expressions["out"])
	
	# Test evaluation
	var result = toggle.evaluate_expression()
	
	print("  Result after evaluation:", result)
	
	if result != null and result.has("out"):
		print("  ✅ Expression evaluation successful")
		print("  New state: out =", result["out"])
	else:
		print("  ❌ Expression evaluation failed (returned null)")
	
	toggle.queue_free()

func test_expression_generation():
	print("\n4. Testing Expression Generation with Cycles:")
	
	# Test the generate_complete_boolean_expression function
	var test_gate = Gate2D.new()
	test_gate.gate_name = "TEST_CYCLE"
	test_gate.input_pins = {"a": false}
	test_gate.output_pins = {"out": false}
	test_gate.boolean_expressions = {"out": "a or prev_out"}
	
	var generated = test_gate.generate_complete_boolean_expression()
	
	print("  Generated expressions:")
	for key in generated.keys():
		print("    ", key, ": ", generated[key])
	
	test_gate.queue_free()
	
	print("\n=== Test Complete ===")
