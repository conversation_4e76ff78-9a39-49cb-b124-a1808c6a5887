[gd_scene load_steps=11 format=3 uid="uid://oxg7cm8oatig"]

[ext_resource type="Script" uid="uid://ciwgyn7euom2e" path="res://classes/wire 2d/wire_2d.gd" id="1_28uyb"]
[ext_resource type="FontFile" uid="uid://berob6jst1snq" path="res://fonts/Poppins/Poppins-Bold.ttf" id="2_djj10"]

[sub_resource type="SegmentShape2D" id="SegmentShape2D_28uyb"]
b = Vector2(0, 0)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_djj10"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_djj10"]
bg_color = Color(0.111197, 0.111197, 0.111197, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gfcq1"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_28uyb"]
content_margin_left = 10.0
content_margin_top = 5.0
content_margin_right = 10.0
content_margin_bottom = 5.0
bg_color = Color(0.778426, 0.220164, 0.218235, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="Animation" id="Animation_djj10"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Node2D/Button:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1e-05, 1e-05)]
}

[sub_resource type="Animation" id="Animation_28uyb"]
resource_name = "reveal button"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Node2D/Button:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.5),
"transitions": PackedFloat32Array(3.03144, 1, 1),
"update": 0,
"values": [Vector2(1e-05, 1e-05), Vector2(1, 1), Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_djj10"]
_data = {
&"RESET": SubResource("Animation_djj10"),
&"reveal button": SubResource("Animation_28uyb")
}

[node name="Wire 2D" type="Line2D"]
z_index = -1
width = 2.0
default_color = Color(1, 1, 0.133333, 0.811765)
script = ExtResource("1_28uyb")
tracing_color = Color(0.662745, 0.776471, 1, 0.662745)
completed_color = Color(1, 1, 1, 0.745098)
selected_color = Color(0.78, 0.2184, 0.2184, 0.788235)
activated_color = Color(0.429335, 1, 0.40842, 1)

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("SegmentShape2D_28uyb")

[node name="Node2D" type="Node2D" parent="."]

[node name="Button" type="Button" parent="Node2D"]
offset_left = 10.0
offset_top = 13.0
offset_right = 69.0
offset_bottom = 44.0
scale = Vector2(1e-05, 1e-05)
theme_override_fonts/font = ExtResource("2_djj10")
theme_override_styles/focus = SubResource("StyleBoxEmpty_djj10")
theme_override_styles/hover = SubResource("StyleBoxFlat_djj10")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gfcq1")
theme_override_styles/normal = SubResource("StyleBoxFlat_28uyb")
text = "Delete"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_djj10")
}

[connection signal="pressed" from="Node2D/Button" to="." method="_on_button_pressed"]
